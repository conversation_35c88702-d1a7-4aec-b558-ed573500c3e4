{"name": "MatchQuery", "schema": {"type": "object", "properties": {"$sort": {"type": "object", "properties": {"id": {"maximum": 1, "minimum": -1, "type": "integer"}, "legacyId": {"maximum": 1, "minimum": -1, "type": "integer"}, "name": {"maximum": 1, "minimum": -1, "type": "integer"}, "isActive": {"maximum": 1, "minimum": -1, "type": "integer"}, "startDate": {"maximum": 1, "minimum": -1, "type": "integer"}, "endDate": {"maximum": 1, "minimum": -1, "type": "integer"}, "federationId": {"maximum": 1, "minimum": -1, "type": "integer"}, "organizerId": {"maximum": 1, "minimum": -1, "type": "integer"}, "latitude": {"maximum": 1, "minimum": -1, "type": "integer"}, "longitude": {"maximum": 1, "minimum": -1, "type": "integer"}, "tournamentId": {"maximum": 1, "minimum": -1, "type": "integer"}}, "additionalProperties": false}, "$select": {"maxItems": 11, "type": "array", "items": {"enum": ["id", "legacyId", "name", "isActive", "startDate", "endDate", "federationId", "organizerId", "latitude", "longitude", "tournamentId"], "type": "string"}}, "$and": {"type": "array", "items": {"anyOf": [{"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "startDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "endDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "federationId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "latitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "longitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "tournamentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}, {"type": "object", "properties": {"$or": {"type": "array", "items": {"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "startDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "endDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "federationId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "latitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "longitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "tournamentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}}}, "required": ["$or"]}]}}, "$or": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "startDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "endDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "federationId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "latitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "longitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "tournamentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}, "additionalProperties": false}}, "id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "startDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "endDate": {"anyOf": [{"format": "date", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date", "type": "string"}, "$gte": {"format": "date", "type": "string"}, "$lt": {"format": "date", "type": "string"}, "$lte": {"format": "date", "type": "string"}, "$ne": {"format": "date", "type": "string"}, "$in": {"type": "array", "items": {"format": "date", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date", "type": "string"}}}}]}, "federationId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "latitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "longitude": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "tournamentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "$populate": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "$populateCount": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "additionalProperties": false}}