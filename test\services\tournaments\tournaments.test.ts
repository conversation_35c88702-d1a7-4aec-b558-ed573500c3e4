// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('tournaments service', () => {
  const service = app.service('tournaments')
  const usersService = app.service('users')
  const authService = app.service('authentication')
  const organizersService = app.service('organizers')
  const matchesService = app.service('matches')

  // Sample tournament data for testing
  const testTournament = {
    name: 'Test Tournament 2025',
    description: 'This is a test tournament',
    coverImageUrl: 'https://example.com/cover.jpg',
    icon: 'https://example.com/icon.png'
  }

  // Sample match data for testing
  const testMatch = {
    name: 'Test Match in Tournament',
    isActive: true,
    description: 'This is a test match in a tournament',
    maxPlayersAmount: 10,
    styleDivisions: JSON.stringify(['recurve', 'compound']),
    ageDivisions: JSON.stringify(['senior', 'junior']),
    forWomen: true,
    forMen: true
  }

  let userId: number
  let tournamentId: number
  let organizerId: number
  let accessToken: string
  let userParams: any

  // Create a test user before running tournament tests
  before(async () => {
    const testUser = await usersService.create({
      email: `test-tournaments-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }

    // Create a test organizer
    const organizer = await createTestOrganizer(userId, userParams)
    organizerId = organizer.id
  })

  // Clean up test user and organizer after tests
  after(async () => {
    try {
      // Clean up in the correct order due to foreign key constraints
      if (organizerId) {
        await organizersService.remove(organizerId, userParams)
      }
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(error, 'Returns an error for unauthenticated access')
    }
  })

  it('creates a tournament', async () => {
    const createdTournament = await service.create({
      ...testTournament,
      organizerId
    }, userParams)

    assert.ok(createdTournament, 'Created a tournament')
    assert.strictEqual(createdTournament.name, testTournament.name, 'Sets the name')
    assert.strictEqual(createdTournament.description, testTournament.description, 'Sets the description')
    assert.strictEqual(createdTournament.coverImageUrl, testTournament.coverImageUrl, 'Sets the coverImageUrl')
    assert.strictEqual(createdTournament.icon, testTournament.icon, 'Sets the icon')
    assert.strictEqual(createdTournament.organizerId, organizerId, 'Sets the organizerId')
    assert.strictEqual(createdTournament.isActive, true, 'Sets isActive to true by default')
    assert.ok(createdTournament.createdAt, 'Sets createdAt')
    assert.ok(createdTournament.updatedAt, 'Sets updatedAt')
    assert.strictEqual(createdTournament.createdBy, userId, 'Sets createdBy')
    assert.strictEqual(createdTournament.updatedBy, userId, 'Sets updatedBy')

    // Save the tournament ID for later tests
    tournamentId = createdTournament.id
  })

  it('gets a tournament with virtual organizer field', async () => {
    const fetchedTournament = await service.get(tournamentId, userParams)

    assert.ok(fetchedTournament, 'Got the tournament')
    assert.ok(fetchedTournament.organizer, 'Includes the organizer')
    assert.strictEqual(fetchedTournament.organizer.id, organizerId, 'Organizer has the correct ID')
    assert.strictEqual(fetchedTournament.organizer.name, 'Test Organizer', 'Organizer has the correct name')
  })

  it('updates a tournament', async () => {
    const updatedTournament = await service.patch(tournamentId, {
      name: 'Updated Tournament Name',
      description: 'Updated description'
    }, userParams)

    assert.ok(updatedTournament, 'Updated the tournament')
    assert.strictEqual(updatedTournament.name, 'Updated Tournament Name', 'Updated the name')
    assert.strictEqual(updatedTournament.description, 'Updated description', 'Updated the description')
    assert.strictEqual(updatedTournament.updatedBy, userId, 'Sets updatedBy')
  })

  it('creates a match with a tournament reference', async () => {
    const match = await matchesService.create({
      ...testMatch,
      organizerId,
      tournamentId
    }, userParams)

    assert.ok(match, 'Created a match')
    assert.strictEqual(match.tournamentId, tournamentId, 'Sets the tournamentId')
  })

  it('finds matches by tournament', async () => {
    const matches = await matchesService.find({
      query: {
        tournamentId
      },
      ...userParams
    })

    assert.ok(matches.data.length > 0, 'Found matches for the tournament')
    assert.strictEqual(matches.data[0].tournamentId, tournamentId, 'Match has the correct tournamentId')
  })

  it('populates matches array when requested', async () => {
    const tournament = await service.get(tournamentId, {
      ...userParams,
      query: {
        $populate: ['matches']
      }
    })

    assert.ok(tournament, 'Got the tournament')
    assert.ok(Array.isArray(tournament.matches), 'Tournament has matches array')
    assert.ok(tournament.matches && tournament.matches.length > 0, 'Tournament has at least one match')
    const firstMatch = tournament.matches![0] as any
    assert.strictEqual(firstMatch.tournamentId, tournamentId, 'Match belongs to tournament')
    assert.ok(firstMatch.name, 'Match has name field')
  })

  it('populates organizer when requested', async () => {
    const tournament = await service.get(tournamentId, {
      ...userParams,
      query: {
        $populate: ['organizer']
      }
    })

    assert.ok(tournament, 'Got the tournament')
    assert.ok(tournament.organizer, 'Tournament has organizer')
    assert.strictEqual(tournament.organizer.id, organizerId, 'Organizer has correct ID')
    assert.ok(tournament.organizer.name, 'Organizer has name field')
  })

  it('populates multiple fields when requested', async () => {
    const tournament = await service.get(tournamentId, {
      ...userParams,
      query: {
        $populate: ['matches', 'organizer']
      }
    })

    assert.ok(tournament, 'Got the tournament')
    assert.ok(Array.isArray(tournament.matches), 'Tournament has matches array')
    assert.ok(tournament.organizer, 'Tournament has organizer')
    assert.strictEqual(tournament.organizer.id, organizerId, 'Organizer has correct ID')
    assert.ok(tournament.matches.length > 0, 'Tournament has at least one match')
  })

  it('removes a tournament', async () => {
    // First, remove any matches associated with the tournament
    const matches = await matchesService.find({
      query: {
        tournamentId
      },
      ...userParams
    })

    for (const match of matches.data) {
      await matchesService.remove(match.id, userParams)
    }

    // Now remove the tournament
    const removedTournament = await service.remove(tournamentId, userParams)

    assert.ok(removedTournament, 'Removed the tournament')
    assert.strictEqual(removedTournament.id, tournamentId, 'Removed the correct tournament')
    assert.ok(removedTournament.deletedAt, 'Tournament has deletedAt set')
    assert.strictEqual(removedTournament.deletedBy, userId, 'Tournament has deletedBy set')
  })
})
