<script setup lang="ts">
import type { Tournament, Match, MatchRegistration } from '@/api/feathers-client'
import { cn } from '@/lib/utils'
import { Calendar, Shield, RotateCcw, Building2 } from 'lucide-vue-next'
import { computed, ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { useMatchesService } from '@/stores/matches'

// Extended Tournament type with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: Match[]
}

const props = defineProps<{
  tournament: TournamentWithMatches
  isSelected?: boolean
}>()

const emit = defineEmits<{
  select: [id: number]
  hover: [id: number | null]
}>()

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()
const matchesService = useMatchesService()

// State for match registrations
const matchRegistrations = ref<MatchRegistration[]>([])
const isLoadingRegistrations = ref(false)

const handleClick = () => {
  emit('select', props.tournament.id)
}

const handleMouseEnter = () => {
  emit('hover', props.tournament.id)
}

const handleMouseLeave = () => {
  emit('hover', null)
}

const goToTournament = () => {
  router.push({ name: 'tournament-details', params: { id: props.tournament.id.toString() } })
}

const goToOrganizer = () => {
  if (props.tournament.organizerId) {
    router.push({ name: 'organizer-details', params: { id: props.tournament.organizerId.toString() } })
  }
}

// Fetch match registrations for the active player
const fetchMatchRegistrations = async () => {
  if (!userStore.activePlayer?.id || !props.tournament.matches) return

  isLoadingRegistrations.value = true
  try {
    const matchIds = props.tournament.matches.map(match => match.id)
    const registrations = await matchesService.findMatchRegistrations({
      query: {
        playerId: userStore.activePlayer.id,
        matchId: { $in: matchIds },
        $limit: 100
      }
    })
    matchRegistrations.value = registrations || []
  } catch (err) {
    console.error('Failed to fetch match registrations:', err)
    matchRegistrations.value = []
  } finally {
    isLoadingRegistrations.value = false
  }
}

// Watch for changes in tournament or active player
watch([() => props.tournament.id, () => userStore.activePlayer?.id], () => {
  fetchMatchRegistrations()
}, { immediate: true })

// Check if player is registered for a specific match
const isPlayerRegistered = (matchId: number) => {
  return matchRegistrations.value.some(reg => reg.matchId === matchId)
}

const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })
}

// Generate dots for tournament rounds
const roundDots = computed(() => {
  const totalRounds = props.tournament.totalRounds || 0
  const matches = props.tournament.matches || []
  const now = new Date()
  const dots = []

  // Sort matches by startDate to ensure proper order
  const sortedMatches = [...matches].sort((a, b) => {
    if (!a.startDate) return 1
    if (!b.startDate) return -1
    return new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  })

  for (let i = 1; i <= totalRounds; i++) {
    // Find match for this round (assuming matches are ordered by round/date)
    const matchForRound = sortedMatches[i - 1] // Simple assumption: matches are in order

    let state: 'past' | 'future' | 'missing' = 'missing'

    if (matchForRound) {
      if (matchForRound.endDate && new Date(matchForRound.endDate) < now) {
        state = 'past'
      } else if (matchForRound.startDate && new Date(matchForRound.startDate) > now) {
        state = 'future'
      } else if (matchForRound.startDate && new Date(matchForRound.startDate) <= now) {
        // If start date is in the past but no end date, consider it ongoing (future)
        state = 'future'
      }
    }

    // Check if player is registered for this match
    const isRegistered = matchForRound ? isPlayerRegistered(matchForRound.id) : false

    dots.push({
      round: i,
      state,
      hasMatch: !!matchForRound,
      match: matchForRound,
      isRegistered
    })
  }

  return dots
})

const organizerName = computed(() => {
  return props.tournament.organizer?.name || `Organizer ${props.tournament.organizerId}`
})

const federationName = computed(() => {
  return props.tournament.federation?.name || 'N/A'
})

// Compute date range from first to last match
const dateRange = computed(() => {
  const matches = props.tournament.matches || []
  if (matches.length === 0) {
    return formatDate(props.tournament.createdAt) // Fallback to creation date
  }

  // Filter matches with valid start dates and sort them
  const validMatches = matches
    .filter(match => match.startDate)
    .sort((a, b) => new Date(a.startDate!).getTime() - new Date(b.startDate!).getTime())

  if (validMatches.length === 0) {
    return formatDate(props.tournament.createdAt) // Fallback to creation date
  }

  const firstMatch = validMatches[0]
  const lastMatch = validMatches[validMatches.length - 1]

  const firstDate = formatDate(firstMatch.startDate)
  const lastDate = formatDate(lastMatch.startDate)

  // If same date, show only one date
  if (firstDate === lastDate) {
    return firstDate
  }

  return `${firstDate} - ${lastDate}`
})
</script>

<template>
  <div
    :class="cn(
      'flex flex-col gap-2 rounded-lg border p-3 text-left text-sm transition-all',
      (props.tournament as any).read ? 'bg-card' : 'bg-primary/5',
      (props.tournament as any).isHighlighted && 'border-l-4 border-l-primary',
      props.isSelected
        ? 'bg-blue-500/10'
        : 'hover:bg-accent/20'
    )"
    role="button"
    tabindex="0"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Tournament name and status -->
    <div class="flex items-start justify-between gap-2">
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-base truncate cursor-pointer hover:text-primary" @click.stop="goToTournament">
          {{ tournament.name }}
        </h3>
        <div class="flex items-center gap-2 mt-1">
          <Building2 class="h-4 w-4 text-yellow-500" />
          <span
            class="text-xs text-muted-foreground cursor-pointer hover:text-primary hover:underline"
            @click.stop="goToOrganizer"
          >
            {{ organizerName }}
          </span>
        </div>
      </div>
      <div class="flex flex-col items-end gap-1">
        <span v-if="!tournament.completedAt" class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
          {{ t('tournament.ongoing') }}
        </span>
        <span v-else class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
          {{ t('tournament.ended') }}
        </span>
      </div>
    </div>

    <!-- Tournament rounds dots -->
    <div v-if="roundDots.length > 0" class="flex items-center gap-1 mt-2">
      <span class="text-xs text-muted-foreground mr-2">{{ t('tournament.rounds') }}:</span>
      <div class="flex gap-1">
        <div
          v-for="dot in roundDots"
          :key="dot.round"
          :class="cn(
            'w-3 h-3 rounded-full border-2',
            // Registration status colors (green) take priority
            dot.isRegistered && dot.state === 'past' && 'bg-green-800 border-green-800',
            dot.isRegistered && dot.state === 'future' && 'bg-green-500 border-green-500',
            // Default colors when not registered
            !dot.isRegistered && dot.state === 'past' && 'bg-gray-600 border-gray-600',
            !dot.isRegistered && dot.state === 'future' && 'bg-blue-600 border-blue-600',
            dot.state === 'missing' && 'bg-gray-300 border-gray-300'
          )"
          :title="`${t('tournament.round')} ${dot.round} - ${dot.state === 'past' ? t('tournament.past') : dot.state === 'future' ? t('tournament.future') : t('tournament.missing')}${dot.isRegistered ? ' (' + t('tournament.registered') + ')' : ''}`"
        />
      </div>
    </div>

    <!-- Tournament details -->
    <div class="flex flex-col gap-2 mt-1 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-1.5">
          <Calendar class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ dateRange }}</span>
        </div>
        <div v-if="federationName !== 'N/A'" class="flex items-center gap-1.5">
          <Shield class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ federationName }}</span>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <div v-if="tournament.totalRounds" class="flex items-center gap-1.5">
          <RotateCcw class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ tournament.totalRounds }} {{ t('tournament.rounds') }}</span>
        </div>
      </div>
    </div>

    <!-- Tournament description preview -->
    <div v-if="tournament.description" class="mt-2">
      <p class="text-xs text-muted-foreground line-clamp-2">
        {{ tournament.description }}
      </p>
    </div>
  </div>
</template>
