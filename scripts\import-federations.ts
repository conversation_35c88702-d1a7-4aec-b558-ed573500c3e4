import { promises as fs } from 'fs'
import path from 'path'

import { parse as csvParse } from 'csv-parse/sync'

import { app } from '../src/app'
import type { FederationData } from '../src/services/federations/federations.schema'

function parseBool(val: string): boolean {
    return val === '1' || val === 'true'
}

// Updated parseJsonField to return JS object for JSONB columns
const parseJsonField = (value: string | undefined): any => {
    if (!value || value === '\\N' || value === '-1') return undefined;
    try {
        return JSON.parse(value);
    } catch (error) {
        console.error(`Error parsing JSON value: ${value}`, error);
        return undefined;
    }
};

const parseString = (value: string | undefined): string | undefined => {
    if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
        return undefined;
    }
    return value.trim();
};

async function importFederations(csvFile: string) {
    const absPath = path.resolve(csvFile)
    let fileContent = await fs.readFile(absPath, 'utf8')
    // Remove UTF-8 BOM if present
    if (fileContent.charCodeAt(0) === 0xFEFF) {
        fileContent = fileContent.slice(1)
    }
    const records: any[] = csvParse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        delimiter: ';'
    })

    for (const row of records) {
        console.log(row)
        try {
            let data: FederationData = {
                legacyId: Number(row['id']),
                isActive: parseBool(row['active']),
                name: parseString(row['name']) || 'Unnamed Federation',
                fullName: parseString(row['full_name']),
                ageDivisions: parseJsonField(row['age_categories']) as any,
                styleDivisions: parseJsonField(row['equipment_categories']) as any,
                disciplines: parseJsonField(row['competition_branches']) as any
            }
            // Remove undefined fields to avoid DB DEFAULT issues
            data = Object.fromEntries(Object.entries(data).filter(([_, v]) => v !== undefined)) as FederationData;
            // Debug: log types of JSON fields
            console.log('Prepared data:', data, {
                ageDivisionsType: typeof data.ageDivisions,
                styleDivisionsType: typeof data.styleDivisions,
                disciplinesType: typeof data.disciplines
            });
            await app.service('federations').create(data)
            console.log(`Imported federation: ${data.name} (legacyId: ${data.legacyId})`)
        } catch (err) {
            console.error(
                `Failed to import federation with id=${row['id']}, name=${row['name']}:`,
                (err as any).message
            )
        }
    }
}

if (process.argv.length < 3) {
    console.error('Usage: pnpm tsx scripts/import-federations.ts <path-to-csv>')
    process.exit(1)
}

importFederations(process.argv[2])
    .then(() => {
        console.log('Federations import complete.')
        process.exit(0)
    })
    .catch((err) => {
        console.error('Import failed:', err)
        process.exit(1)
    })
