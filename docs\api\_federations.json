{"endpoint": "/federations", "methods": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "schema": {"type": "integer"}}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "schema": {"type": "integer"}}, {"description": "Property to sort results", "in": "query", "name": "$sort", "style": "deepObject", "schema": {"type": "object"}}, {"description": "Query parameters to filter", "in": "query", "name": "filter", "style": "form", "explode": true, "schema": {"$ref": "#/components/schemas/federations"}}], "responses": {"200": {"description": "List of federations", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "number"}, "limit": {"type": "number"}, "skip": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Federation"}}}}}}}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["federations"], "security": []}, "post": {"parameters": [], "responses": {"201": {"description": "Federation created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Federation"}}}}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["federations"], "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FederationData"}}}}}}}