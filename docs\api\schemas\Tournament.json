{"name": "Tournament", "schema": {"required": ["id", "organizerId", "name"], "type": "object", "properties": {"id": {"type": "number"}, "organizerId": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "coverImageUrl": {"type": "string"}, "icon": {"type": "string"}, "isActive": {"type": "boolean"}, "legacyId": {"type": "number"}, "styleDivisions": {}, "ageDivisions": {}, "forWomen": {"type": "boolean"}, "forMen": {"type": "boolean"}, "isOpen": {"type": "boolean"}, "federationId": {"type": "number"}, "licenseRequired": {"type": "boolean"}, "international": {"type": "boolean"}, "completedAt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "rulesSettings": {"type": "string"}, "totalRounds": {"type": "number"}, "minRounds": {"type": "number"}, "agenda": {}, "generalScore": {}, "competitionLevel": {"type": "string"}, "yearly": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "organizer": {"$ref": "#/components/schemas/Organizer"}, "federation": {"$ref": "#/components/schemas/Federation"}, "matches": {"type": "array", "items": {}}}, "additionalProperties": false}}