{"name": "Match", "schema": {"required": ["id", "name", "isActive"], "type": "object", "properties": {"id": {"type": "number"}, "legacyId": {"type": "number"}, "name": {"type": "string"}, "isActive": {"type": "boolean"}, "country": {"type": "string"}, "city": {"type": "string"}, "postcode": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "registrationEnds": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "coverImageUrl": {"type": "string"}, "matchType": {"type": "string"}, "styleDivisions": {}, "ageDivisions": {}, "forWomen": {"type": "boolean"}, "forMen": {"type": "boolean"}, "federationId": {"type": "number"}, "licenseRequired": {"type": "boolean"}, "organizerId": {"type": "number"}, "maxPlayersAmount": {"type": "number"}, "payments": {}, "competitionLevel": {"type": "string"}, "international": {"type": "boolean"}, "withoutLimits": {"type": "boolean"}, "publishAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "agenda": {}, "currency": {"type": "string"}, "judges": {}, "registrationFinished": {"type": "boolean"}, "attachments": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "tournamentId": {"type": "number"}, "tournamentConfirmedAt": {"type": "string", "format": "date-time"}, "yearly": {"type": "boolean"}, "organizer": {"$ref": "#/components/schemas/Organizer"}, "tournament": {"$ref": "#/components/schemas/Tournament"}, "federation": {"$ref": "#/components/schemas/Federation"}, "matchRegistrationsCount": {"type": "number"}}, "additionalProperties": false}}