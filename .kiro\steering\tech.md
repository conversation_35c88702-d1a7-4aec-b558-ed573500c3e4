# Technology Stack

## Framework & Runtime
- **Node.js**: v22.14.0+ (specified in engines)
- **FeathersJS**: v5.0.34 - Real-time API framework with REST and WebSocket support
- **Koa**: Web framework for HTTP handling
- **TypeScript**: Primary language with strict mode enabled

## Database & ORM
- **PostgreSQL**: Primary database
- **Knex.js**: SQL query builder and migration tool
- **@feathersjs/knex**: Feathers database adapter

## Authentication & Security
- **@feathersjs/authentication**: JWT-based auth with local and OAuth strategies
- **feathers-authentication-management**: User account management (verification, password reset)
- **OAuth Providers**: GitHub, Google, Facebook

## Package Management
- **pnpm**: v10.11.0 (specified package manager)

## Development Tools
- **ts-node**: TypeScript execution for development
- **nodemon**: Auto-restart during development
- **Prettier**: Code formatting
- **Mocha**: Testing framework
- **cross-env**: Environment variable management

## Additional Libraries
- **winston**: Logging
- **nodemailer**: Email functionality
- **csv-parse/csv-parser**: CSV data import
- **feathers-swagger**: API documentation
- **handlebars**: Template engine
- **axios**: HTTP client for external API calls
- **@feathersjs/typebox**: Schema validation with TypeBox

## Common Commands

### Development
```bash
pnpm dev              # Start development server with auto-reload
pnpm compile          # Compile TypeScript to JavaScript
pnpm start            # Start production server
pnpm prettier         # Format code
```

### Database
```bash
pnpm migrate          # Run latest migrations
pnpm migrate:down     # Rollback last migration
pnpm migrate:make     # Create new migration
```

### Testing
```bash
pnpm test             # Run tests with fresh test DB
pnpm test:clean       # Run tests with clean test DB
pnpm setup-test-db    # Setup test database only
pnpm clean-test-db    # Clean test database
```

### Data Import
```bash
pnpm batch-import     # Run batch import scripts
```

### Versioning
```bash
pnpm version:patch    # Bump patch version
pnpm version:minor    # Bump minor version
pnpm version:major    # Bump major version
```

## Build Configuration
- **Output**: `lib/` directory
- **Source**: `src/` directory
- **Target**: ES2020
- **Module**: CommonJS
- **Source Maps**: Enabled
- **Declarations**: Generated (.d.ts files)

## Development Guidelines

### Code Style & Best Practices
- **Comments**: Generate comments only for complex logic
- **TypeScript**: Use TypeScript for type safety with strict mode
- **Async Operations**: Use async/await for asynchronous operations
- **Error Handling**: Implement proper error handling
- **Architecture**: Follow Single Responsibility Principle
- **Package Manager**: Use `pnpm dlx` instead of `npx`

### Database Conventions
- **Column Naming**: Use camelCase for all column names
- **Standard Columns**: Always implement createdAt, updatedAt, deletedAt, createdBy, updatedBy, deletedBy
- **Complex Data**: Use JSONB for objects, arrays, or complex data structures
- **JSON Storage**: JSON and JSONB fields need to be stringified before storing in DB
- **Migration Naming**: Use timestamp precision with hours and minutes (YYYYMMDDHHMMSS_description.ts)
- **Migration Creation**: Generate migration files manually, don't use CLI
- **Migration Execution**: Never run migrations automatically - let developers handle them

### Data Import Rules
- **CSV Handling**: Always handle "\\N" values by parsing them to null/undefined
- **Testing**: Run tests yourself and validate results when needed