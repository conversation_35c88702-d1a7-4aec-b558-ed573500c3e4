# Technology Stack

## Core Framework & Build System
- **Vue 3** with Composition API and `<script setup>` syntax
- **Vite** for build tooling and development server
- **TypeScript** for type safety
- **pnpm** as package manager

## UI & Styling
- **Tailwind CSS** v4 for styling
- **shadcn-vue** component library (New York style)
- **Reka UI** for advanced components
- **Lucide Vue Next** for icons
- **Vaul Vue** for drawer components

## State Management & Routing
- **Pinia** for state management
- **Vue Router** for client-side routing
- **VueUse** for composition utilities

## Backend Integration
- **FeathersJS 5** client for API communication with typed client
- **Socket.io** for real-time WebSocket connections
- **Axios** for HTTP requests
- Custom API package: `ap-api-feathers` (typed client)
- API schemas located in `/docs/api/schemas/` with `schemasIndex.json`

## Additional Libraries
- **Vue I18n** for internationalization
- **date-fns** and **dayjs** for date manipulation
- **Leaflet** for interactive maps
- **Tanstack Vue Table** for data tables

## Development Tools
- **ESLint** with Vue and TypeScript configs
- **Prettier** for code formatting
- **Vitest** for unit testing
- **Playwright** for E2E testing
- **Vue DevTools** for debugging

## Common Commands

### Development
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm preview      # Preview production build
```

### Testing
```bash
pnpm test:unit    # Run unit tests with Vitest
pnpm test:e2e     # Run E2E tests with Playwright
```

### Code Quality
```bash
pnpm lint         # Lint and fix code with ESLint
pnpm format       # Format code with Prettier
pnpm type-check   # Type check with vue-tsc
```

### Documentation
```bash
pnpm docs:api     # Generate API documentation from Swagger
```

## Code Style Rules
- **No semicolons** (Prettier config)
- **Single quotes** for strings
- **100 character** line width
- **CSS variables** enabled for Tailwind
- **@ alias** for src directory imports
- Use `pnpm dlx` instead of `npx`

## Vue Component Guidelines
- Use **Composition API** with `<script setup>` syntax
- Component names should be **multi-word** (avoid HTML conflicts)
- **Explicitly type props** with TypeScript
- Use **defineEmits** with TypeScript types
- Prefer **v-model binding** over properties when possible

## State Management Patterns
- Create **separate Pinia stores** for different domains
- Use **TypeScript** for store state, getters, and actions
- Prefer **composition API style** for Pinia stores
- Use types from `ap-api-feathers` for API objects

## Authentication
- Authentication handled by **FeathersJS backend**
- Client configured in `src/api/feathers-client.ts`
- Auth store in `src/stores/auth.ts`

## Internationalization
- Use **vue-i18n** with files in `/src/i18n/locales/`
- Add **English translations only** unless specified
- Use `useI18n()` composable with destructured `t` function
- Prefer **translations in templates**, use computed properties in script blocks
- **Always use i18n** for hardcoded texts

## Best Practices
- Use **async/await** for asynchronous operations
- Implement **proper error handling**
- Use **composables** for reusable logic
- Follow **Single Responsibility Principle**
- **Render dates** in human-readable format
- Use **named routes** for navigation
- Implement **route guards** for authentication
- Use **mobile-first** approach for styling