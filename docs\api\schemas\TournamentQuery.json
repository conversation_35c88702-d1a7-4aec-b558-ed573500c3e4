{"name": "<PERSON><PERSON><PERSON>y", "schema": {"type": "object", "properties": {"$sort": {"type": "object", "properties": {"id": {"maximum": 1, "minimum": -1, "type": "integer"}, "organizerId": {"maximum": 1, "minimum": -1, "type": "integer"}, "name": {"maximum": 1, "minimum": -1, "type": "integer"}, "isActive": {"maximum": 1, "minimum": -1, "type": "integer"}, "legacyId": {"maximum": 1, "minimum": -1, "type": "integer"}, "completedAt": {"maximum": 1, "minimum": -1, "type": "integer"}}, "additionalProperties": false}, "$select": {"maxItems": 6, "type": "array", "items": {"enum": ["id", "organizerId", "name", "isActive", "legacyId", "completedAt"], "type": "string"}}, "$and": {"type": "array", "items": {"anyOf": [{"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "completedAt": {"anyOf": [{"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$gte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$ne": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$in": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}, "$nin": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}}}]}}}, {"type": "object", "properties": {"$or": {"type": "array", "items": {"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "completedAt": {"anyOf": [{"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$gte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$ne": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$in": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}, "$nin": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}}}]}}}}}, "required": ["$or"]}]}}, "$or": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "completedAt": {"anyOf": [{"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$gte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$ne": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$in": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}, "$nin": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}}}]}}, "additionalProperties": false}}, "id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "organizerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "name": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "isActive": {"anyOf": [{"type": "boolean"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "boolean"}, "$gte": {"type": "boolean"}, "$lt": {"type": "boolean"}, "$lte": {"type": "boolean"}, "$ne": {"type": "boolean"}, "$in": {"type": "array", "items": {"type": "boolean"}}, "$nin": {"type": "array", "items": {"type": "boolean"}}}}]}, "legacyId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "completedAt": {"anyOf": [{"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$gte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lt": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$lte": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$ne": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}, "$in": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}, "$nin": {"type": "array", "items": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}]}}}}]}, "$populate": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "additionalProperties": false}}