# Project Structure

## Root Directory Layout
```
├── src/                    # TypeScript source code
├── lib/                    # Compiled JavaScript output
├── test/                   # Test files and fixtures
├── migrations/             # Database migration files
├── scripts/                # Utility and import scripts
├── config/                 # Environment-specific configurations
├── public/                 # Static files and API packages
├── docs/                   # Documentation
└── logs/                   # Application logs
```

## Source Code Organization (`src/`)
- **app.ts**: Main application setup and configuration
- **index.ts**: Application entry point
- **authentication.ts**: Auth configuration and strategies
- **channels.ts**: Real-time channel configuration
- **postgresql.ts**: Database connection setup
- **logger.ts**: Winston logging configuration
- **declarations.ts**: TypeScript type declarations
- **validators.ts**: Schema validation utilities
- **services/**: Feature-based service modules
- **hooks/**: Reusable middleware hooks

## Services Structure (`src/services/`)
Each service follows FeathersJS conventions:
- **Core Entities**: users, players, organizers, clubs
- **Tournament System**: tournaments, matches, match-registrations, match-results
- **Equipment & Config**: equipment, federations, match-formats
- **Communication**: messages, email, password-reset
- **Utility**: api (general endpoints), user-me (current user)

## Configuration (`config/`)
- **default.json**: Base configuration
- **production.json**: Production overrides
- **test.json**: Test environment settings
- **custom-environment-variables.json**: Environment variable mappings

## Database (`migrations/`)
- Chronologically ordered migration files
- Naming convention: `YYYYMMDDHHMMSS_description.ts`
- Covers schema evolution from initial setup to current state

## Scripts (`scripts/`)
- **Data Import**: Batch import utilities for legacy data
- **Database Management**: Test DB setup/cleanup
- **Platform Scripts**: Both `.sh` (Unix) and `.bat` (Windows) versions

## Testing (`test/`)
- **Unit Tests**: Service-specific tests
- **Integration Tests**: End-to-end API testing
- **Fixtures**: Test data and mock objects
- Uses separate `archery_test` database

## Build Output (`lib/`)
- Compiled JavaScript from TypeScript source
- Source maps for debugging
- Type declaration files (.d.ts)
- Mirrors `src/` directory structure

## Static Assets (`public/`)
- API package distributions (.tgz files)
- Static HTML files
- Swagger UI assets (in development)

## Development Files
- **knexfile.ts**: Database configuration for migrations
- **tsconfig.json**: TypeScript compiler settings
- **.nvmrc**: Node.js version specification
- **.prettierrc**: Code formatting rules