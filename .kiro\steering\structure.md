# Project Structure

## Root Directory
```
├── src/                    # Main application source code
├── public/                 # Static assets (images, favicon)
├── docs/                   # Documentation files
├── examples/               # Example implementations and demos
├── e2e/                    # End-to-end tests
├── scripts/                # Build and utility scripts
├── .kiro/                  # Kiro AI assistant configuration
└── dist/                   # Build output (generated)
```

## Source Directory (`src/`)
```
src/
├── api/                    # API client configuration
│   └── feathers-client.ts  # FeathersJS client setup
├── assets/                 # Static assets (CSS, images, fonts)
│   ├── main.css           # Global styles and Tailwind imports
│   └── logo.svg           # Application logo
├── components/             # Vue components
│   ├── ui/                # shadcn-vue UI components
│   ├── layout/            # Layout components (headers, sidebars)
│   ├── common/            # Shared/reusable components
│   ├── icons/             # Custom icon components
│   ├── map/               # Map-related components
│   ├── matches/           # Match-specific components
│   ├── tournaments/       # Tournament-specific components
│   └── examples/          # Example component implementations
├── i18n/                  # Internationalization
│   ├── locales/           # Translation files
│   └── index.ts           # i18n configuration
├── lib/                   # Utility functions and helpers
│   └── utils.ts           # Common utility functions
├── router/                # Vue Router configuration
│   └── index.ts           # Route definitions
├── services/              # Business logic and API services
│   ├── user-me/           # User-related services
│   ├── sunriseSunset.ts   # Weather/time services
│   └── weatherService.ts  # Weather API integration
├── stores/                # Pinia state management
│   ├── auth.ts            # Authentication state
│   ├── user.ts            # User data state
│   ├── matches.ts         # Match data state
│   ├── tournaments.ts     # Tournament data state
│   └── equipment.ts       # Equipment/gear state
├── types/                 # TypeScript type definitions
├── views/                 # Page-level Vue components
│   ├── HomeView.vue       # Landing/dashboard page
│   ├── LoginView.vue      # Authentication page
│   ├── MatchesView.vue    # Match listing page
│   ├── TournamentsView.vue # Tournament listing page
│   └── ...                # Other page components
├── App.vue                # Root Vue component
└── main.ts                # Application entry point
```

## Key Conventions

### File Naming
- **Components**: PascalCase (e.g., `MainLayout.vue`, `UserProfile.vue`)
- **Views**: PascalCase with "View" suffix (e.g., `HomeView.vue`)
- **Stores**: camelCase (e.g., `auth.ts`, `userProfile.ts`)
- **Services**: camelCase (e.g., `weatherService.ts`)
- **Types**: camelCase for files, PascalCase for interfaces

### Import Aliases
- `@/` maps to `src/` directory
- Use `@/components/ui` for shadcn-vue components
- Use `@/lib/utils` for utility functions

### Component Organization
- **UI components** in `src/components/ui/` (from shadcn-vue)
- **Layout components** in `src/components/layout/`
- **Feature-specific components** in dedicated folders (e.g., `matches/`, `tournaments/`)
- **Shared components** in `src/components/common/`

### State Management
- Each feature has its own Pinia store
- Store files are named after the domain (e.g., `auth.ts`, `matches.ts`)
- Use composition API style for stores

### Testing
- **Unit tests** alongside source files in `__tests__/` folders
- **E2E tests** in dedicated `e2e/` directory
- Test files use `.spec.ts` or `.test.ts` extensions### API In
tegration
- FeathersJS client configured in `src/api/feathers-client.ts`
- Each service should have a separate Pinia store in `src/stores/`
- API object schemas located in `/docs/api/schemas/` with `schemasIndex.json`
- Authentication handled by FeathersJS backend with `@feathersjs/authentication-client`
- Use types from `ap-api-feathers` for API objects (reexported from `feathers-client.ts`)

### Routing Conventions
- Use **named routes** for navigation
- Group routes by feature
- Implement **route guards** for authentication
- Use route meta fields for additional information

### Internationalization
- Translation files in `/src/i18n/locales/`
- Use `useI18n()` composable with destructured `t` function
- Prefer translations in templates over script blocks
- Always use i18n for hardcoded texts