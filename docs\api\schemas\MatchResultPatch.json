{"name": "MatchResultPatch", "schema": {"type": "object", "properties": {"matchRegistrationId": {"type": "number"}, "points": {"type": "number"}, "maxPoints": {"type": "number"}, "place": {"type": "number"}, "scores": {}, "styleDivision": {"type": "string"}, "ageDivision": {"type": "string"}, "genderDivision": {"type": "string"}, "registrationDetails": {}, "equipmentId": {"type": "number"}, "updatedBy": {"type": "number"}}}}