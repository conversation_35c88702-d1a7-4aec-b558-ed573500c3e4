import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_results', (table) => {
        table.integer('matchRegistrationId').references('id').inTable('match_registrations')
        table.string('styleDivision')
        table.string('ageDivision')
        table.string('genderDivision')
        table.integer('equipmentId').references('id').inTable('equipment')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_results', (table) => {
        table.dropColumn('matchRegistrationId')
        table.dropColumn('styleDivision')
        table.dropColumn('ageDivision')
        table.dropColumn('genderDivision')
        table.dropColumn('equipmentId')
    })
}
