<script setup lang="ts">
import { computed } from 'vue'
import { CalendarDate, DateFormatter, getLocalTimeZone, parseDate, today } from '@internationalized/date'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import dayjs from 'dayjs'

interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Pick a date and time'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | undefined]
}>()

const df = new DateFormatter('en-US', {
  dateStyle: 'medium'
})

const dateValue = computed({
  get: () => {
    if (!props.modelValue) return undefined
    try {
      const date = dayjs(props.modelValue)
      return parseDate(date.format('YYYY-MM-DD'))
    } catch {
      return undefined
    }
  },
  set: (val) => {
    if (val) {
      const currentTime = props.modelValue ? dayjs(props.modelValue).format('HH:mm') : '09:00'
      const newDateTime = dayjs(`${val.toString()} ${currentTime}`).toISOString()
      emit('update:modelValue', newDateTime)
    } else {
      emit('update:modelValue', undefined)
    }
  }
})

const timeValue = computed({
  get: () => {
    if (!props.modelValue) return '09:00'
    try {
      return dayjs(props.modelValue).format('HH:mm')
    } catch {
      return '09:00'
    }
  },
  set: (val) => {
    if (dateValue.value && val) {
      const newDateTime = dayjs(`${dateValue.value.toString()} ${val}`).toISOString()
      emit('update:modelValue', newDateTime)
    }
  }
})

const displayValue = computed(() => {
  if (!props.modelValue) return props.placeholder
  try {
    const date = dayjs(props.modelValue)
    return `${df.format(date.toDate())} at ${date.format('HH:mm')}`
  } catch {
    return props.placeholder
  }
})
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="cn(
          'w-full justify-start text-left font-normal',
          !modelValue && 'text-muted-foreground',
        )"
        :disabled="disabled"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ displayValue }}
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <div class="p-3 space-y-3">
        <Calendar v-model="dateValue" initial-focus />
        <div class="space-y-2">
          <Label for="time">Time</Label>
          <Input
            id="time"
            type="time"
            :value="timeValue"
            @input="timeValue = ($event.target as HTMLInputElement).value"
          />
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>
