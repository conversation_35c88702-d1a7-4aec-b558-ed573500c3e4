import type { Knex, QueryBuilder } from 'knex';

import { HookContext } from '@feathersjs/feathers'

interface PopulateConfig {
  [key: string]: {
    service: string
    localKey: string
    foreignKey: string
    asArray?: boolean
    select?: string[]
  }
}

export const populate = (config: PopulateConfig) => {
  return async (context: HookContext) => {
    const { params, result, app } = context

    // Extract $populate from query params and store for processing
    const populateParam = params.query?.$populate

    // Remove $populate from query to prevent database errors
    if (populateParam) {
      delete params.query.$populate
      // Store it in params for after hook processing
      params.populateParam = populateParam
    }

    // Only process in after hooks when we have results
    if (context.type !== 'after' || !result || !params.populateParam) {
      return context
    }

    const items = Array.isArray(result) ? result :
      (result.data ? result.data : [result])

    // Process populate requests
    const populateFields = Array.isArray(params.populateParam)
      ? params.populateParam
      : [params.populateParam]

    for (const field of populateFields) {
      if (!config[field]) continue

      const populateConfig = config[field]
      const relatedService = app.service(populateConfig.service)

      // Collect all foreign keys to fetch
      const foreignKeys = items
        .map((item: any) => item[populateConfig.localKey])
        .filter((key: any) => key != null)

      console.log(foreignKeys)

      if (foreignKeys.length === 0) continue

      // Fetch related records
      const query: any = {
        [populateConfig.foreignKey]: { $in: foreignKeys }
      }

      if (populateConfig.select) {
        query.$select = populateConfig.select
      }

      const relatedItems = await relatedService.find({
        query,
        paginate: false
      })


      // Map related items back to main items
      const relatedMap = new Map()
      const related = Array.isArray(relatedItems) ? relatedItems : relatedItems.data || []

      for (const relatedItem of related) {
        const key = relatedItem[populateConfig.foreignKey]
        if (populateConfig.asArray) {
          if (!relatedMap.has(key)) {
            relatedMap.set(key, [])
          }
          relatedMap.get(key).push(relatedItem)
        } else {
          relatedMap.set(key, relatedItem)
        }
      }

      // Attach populated data to items
      for (const item of items) {
        const localValue = item[populateConfig.localKey]
        if (localValue != null && relatedMap.has(localValue)) {
          item[field] = relatedMap.get(localValue)
        }
      }
    }

    return context
  }
}

interface CountConfig {
  [field: string]: {
    service: string
    foreignKey: string
    query?: any
  }
}

const toCamelCase = (str: string) => {
  return str.replace(/([-_][a-z])/gi, ($1) => {
    return $1.toUpperCase().replace('-', '').replace('_', '');
  });
};

export const populateCount = (config: CountConfig) => {
  return async (context: HookContext) => {
    const { params, result, app, service } = context

    const countParam = params.query?.$populateCount
    if (countParam) {
      delete params.query.$populateCount
      params.populateCountParam = countParam
    }

    if (context.type !== 'after' || !result || !params.populateCountParam) {
      return context
    }

    const items = Array.isArray(result) ? result : result.data ? result.data : [result]
    if (items.length === 0) {
      return context
    }

    const countFields = Array.isArray(params.populateCountParam)
      ? params.populateCountParam
      : [params.populateCountParam]

    for (const field of countFields) {
      if (!config[field]) {
        continue
      }

      const countConfig = config[field]
      const { service: serviceName, foreignKey, query = {} } = countConfig
      const localKey = service.id

      // We have to cast to `any` because `Model` is a custom property
      // added by feathers-knex, and is not on the base `Service` type.
      const relatedService = app.service(serviceName) as any

      const localKeys = [...new Set(items.map((item: any) => item[localKey]).filter((key: any) => key != null))]

      if (localKeys.length === 0) {
        for (const item of items) {
          item[toCamelCase(`${field}Count`)] = 0
        }
        continue
      }
      const knex = app.get('postgresqlClient')
      const tableName = relatedService.options.name

      const countsQuery = knex(tableName)
        .select(foreignKey) // Also select the foreign key
        .whereIn(foreignKey, localKeys)
        .groupBy(foreignKey)
        .count('* as count')

      if (Object.keys(query).length > 0) {
        countsQuery.andWhere(query) // Apply additional query params
      }

      const counts = await countsQuery

      const countMap = new Map()
      for (const row of counts) {
        countMap.set(row[foreignKey], parseInt(row.count, 10) || 0)
      }

      for (const item of items) {
        const key = item[localKey]
        item[toCamelCase(`${field}Count`)] = countMap.get(key) || 0
      }
    }
    return context
  }
}