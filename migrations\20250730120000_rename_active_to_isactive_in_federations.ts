import { Knex } from 'knex'

// 20250730120000_rename_active_to_isactive_in_federations.ts
// Migration to rename 'active' column to 'isActive' in 'federations' table

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('federations', (table) => {
        table.renameColumn('active', 'isActive')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('federations', (table) => {
        table.renameColumn('isActive', 'active')
    })
}
