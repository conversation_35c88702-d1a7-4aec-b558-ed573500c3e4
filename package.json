{"name": "ap-api-feathers", "description": "Archery Points API", "version": "0.1.11", "homepage": "", "private": true, "keywords": ["archery"], "author": {}, "contributors": [], "bugs": {}, "engines": {"node": ">= 22.14.0"}, "feathers": {"language": "ts", "packager": "pnpm", "database": "postgresql", "framework": "koa", "transports": ["rest", "websockets"], "schema": "typebox"}, "directories": {"lib": "src", "test": "test"}, "files": ["lib/client.js", "lib/**/*.d.ts", "lib/**/*.shared.js"], "main": "lib/client", "scripts": {"dev": "nodemon -x ts-node src/index.ts", "compile": "shx rm -rf lib/ && tsc", "start": "node lib/", "prettier": "npx prettier \"**/*.ts\" --write", "mocha": "cross-env NODE_ENV=test mocha test/ --require ts-node/register --recursive --extension .ts --exit", "setup-test-db": "node scripts/setup-test-db.js", "clean-test-db": "node scripts/clean-test-db.js", "test": "cross-env NODE_ENV=test node scripts/setup-test-db.js && cross-env NODE_ENV=test knex migrate:latest && npm run mocha", "test:clean": "cross-env NODE_ENV=test node scripts/clean-test-db.js && cross-env NODE_ENV=test knex migrate:latest && npm run mocha", "bundle:client": "npm run compile && npm pack --pack-destination ./public", "migrate": "knex migrate:latest", "migrate:down": "knex migrate:down", "migrate:up": "knex migrate:up", "migrate:make": "knex migrate:make", "version:patch": "pnpm version patch", "version:minor": "pnpm version minor", "version:major": "pnpm version major", "batch-import": "pnpm dlx ts-node scripts/batch-import.ts"}, "dependencies": {"@feathersjs/adapter-commons": "~5.0.34", "@feathersjs/authentication": "~5.0.34", "@feathersjs/authentication-client": "~5.0.34", "@feathersjs/authentication-local": "~5.0.34", "@feathersjs/authentication-oauth": "~5.0.34", "@feathersjs/configuration": "~5.0.34", "@feathersjs/errors": "~5.0.34", "@feathersjs/feathers": "~5.0.34", "@feathersjs/hooks": "^0.9.0", "@feathersjs/knex": "~5.0.34", "@feathersjs/koa": "~5.0.34", "@feathersjs/schema": "~5.0.34", "@feathersjs/socketio": "~5.0.34", "@feathersjs/transport-commons": "~5.0.34", "@feathersjs/typebox": "~5.0.34", "@types/nodemailer": "^6.4.17", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "feathers-authentication-management": "^5.1.0", "feathers-hooks-common": "^8.2.1", "feathers-swagger": "^3.0.0", "handlebars": "^4.7.8", "knex": "^3.1.0", "koa-mount": "^4.2.0", "koa-static": "^5.0.0", "nodemailer": "^7.0.3", "pg": "^8.16.0", "swagger-ui-dist": "^5.21.0", "winston": "^3.17.0"}, "devDependencies": {"@feathersjs/cli": "~5.0.34", "@feathersjs/rest-client": "~5.0.34", "@types/mocha": "^10.0.10", "@types/node": "^22.15.18", "axios": "^1.9.0", "cross-env": "^7.0.3", "mocha": "^11.3.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "shx": "^0.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.11.0"}