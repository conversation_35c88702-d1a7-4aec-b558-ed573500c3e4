<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

interface Props {
  latitude?: number | null
  longitude?: number | null
  address?: string
  city?: string
  country?: string
  postcode?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:latitude': [value: number | null]
  'update:longitude': [value: number | null]
  'update:address': [value: string]
  'update:city': [value: string]
  'update:country': [value: string]
  'update:postcode': [value: string]
}>()

const mapContainer = ref<HTMLElement | null>(null)
let map: L.Map | null = null
let marker: <PERSON><PERSON> | null = null

const defaultCenter: L.LatLngTuple = [52.0693, 19.4803] // Poland center
const defaultZoom = 6

// Custom marker icon
const markerIcon = L.divIcon({
  html: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="#ef4444" width="25" height="41">
    <path d="M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z" stroke="#FFFFFF" stroke-width="10"/>
  </svg>`,
  iconSize: [25, 41],
  iconAnchor: [12.5, 41],
  popupAnchor: [1, -34],
  className: 'leaflet-svg-icon'
})

// Geocoding functions
async function geocodeAddress(address: string, city: string, country: string): Promise<{lat: number, lon: number} | null> {
  try {
    const query = [address, city, country].filter(Boolean).join(', ')
    if (!query.trim()) return null

    const response = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(query)}&format=json&limit=1`)
    const data = await response.json()

    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lon: parseFloat(data[0].lon)
      }
    }
    return null
  } catch (error) {
    console.error('Geocoding error:', error)
    return null
  }
}

async function reverseGeocode(lat: number, lon: number): Promise<any> {
  try {
    const response = await fetch(`https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=json`)
    const data = await response.json()
    return data
  } catch (error) {
    console.error('Reverse geocoding error:', error)
    return null
  }
}

// Update marker position
function updateMarker(lat: number, lon: number) {
  if (!map) return

  const position: L.LatLngTuple = [lat, lon]

  if (marker) {
    marker.setLatLng(position)
  } else {
    marker = L.marker(position, { icon: markerIcon })
      .addTo(map)
      .bindPopup('Selected location')
  }

  map.setView(position, Math.max(map.getZoom(), 12))
}

// Handle map click
function onMapClick(e: L.LeafletMouseEvent) {
  const { lat, lng } = e.latlng

  emit('update:latitude', lat)
  emit('update:longitude', lng)

  updateMarker(lat, lng)

  // Reverse geocode to get address
  reverseGeocode(lat, lng).then(data => {
    if (data && data.address) {
      const addr = data.address
      emit('update:address', [addr.house_number, addr.road].filter(Boolean).join(' ') || '')
      emit('update:city', addr.city || addr.town || addr.village || '')
      emit('update:country', addr.country_code?.toUpperCase() || '')
      emit('update:postcode', addr.postcode || '')
    }
  })
}

// Initialize map
function initMap() {
  if (mapContainer.value && !map) {
    map = L.map(mapContainer.value, {
      zoomControl: true,
      attributionControl: true
    })

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map)

    map.on('click', onMapClick)

    // Set initial view
    if (props.latitude && props.longitude) {
      updateMarker(props.latitude, props.longitude)
    } else {
      map.setView(defaultCenter, defaultZoom)
    }
  }
}

// Watch for coordinate changes
watch([() => props.latitude, () => props.longitude], ([newLat, newLng]) => {
  if (newLat && newLng && map) {
    updateMarker(newLat, newLng)
  }
})

// Watch for address changes and geocode
let geocodeTimeout: NodeJS.Timeout | null = null
watch([() => props.address, () => props.city, () => props.country], async ([newAddress, newCity, newCountry]) => {
  if (geocodeTimeout) {
    clearTimeout(geocodeTimeout)
  }

  geocodeTimeout = setTimeout(async () => {
    if (newAddress || newCity || newCountry) {
      const result = await geocodeAddress(newAddress || '', newCity || '', newCountry || '')
      if (result && map) {
        emit('update:latitude', result.lat)
        emit('update:longitude', result.lon)
        updateMarker(result.lat, result.lon)
      }
    }
  }, 1000)
})

function cleanup() {
  if (map) {
    map.remove()
    map = null
    marker = null
  }
}

function handleResize() {
  if (map) {
    map.invalidateSize()
  }
}

onMounted(() => {
  nextTick(() => {
    initMap()
  })
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  cleanup()
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div class="space-y-4">
    <div class="text-sm text-muted-foreground">
      Click on the map to select a location, or enter address details above to automatically place the marker.
    </div>
    <div ref="mapContainer" class="w-full h-64 border rounded-lg"></div>
  </div>
</template>

<style scoped>
:deep(.leaflet-control-container .leaflet-top,
      .leaflet-control-container .leaflet-bottom) {
  z-index: 20;
}

:deep(.leaflet-popup-content) {
  margin: 8px;
  font-size: 0.75rem;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 0.5rem;
}
</style>
