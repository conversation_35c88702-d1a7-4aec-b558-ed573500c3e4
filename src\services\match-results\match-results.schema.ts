// // For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { matchSchema } from '../matches/matches.schema'
import { playerSchema } from '../players/players.schema' // Correct import
import type { MatchResultsService } from './match-results.class'

// Main data model schema
export const matchResultSchema = Type.Object(
  {
    id: Type.Number(),
    matchId: Type.Number(),
    playerId: Type.Number(),
    // New relation to match-registration
    matchRegistrationId: Type.Optional(Type.Number()),
    points: Type.Number(),
    maxPoints: Type.Number(),
    place: Type.Optional(Type.Number()),
    scores: Type.Optional(Type.Unknown()),
    // New fields for registration details
    styleDivision: Type.Optional(Type.String()),
    ageDivision: Type.Optional(Type.String()),
    genderDivision: Type.Optional(Type.String()),
    registrationDetails: Type.Optional(Type.Unknown()),
    equipmentId: Type.Optional(Type.Number()),
    resultDate: Type.String({ format: 'date-time' }),
    createdAt: Type.String({ format: 'date-time' }),
    updatedAt: Type.String({ format: 'date-time' }),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    // Virtual fields for related data
    match: Type.Optional(Type.Ref(matchSchema)),
    player: Type.Optional(Type.Ref(playerSchema)),
    matchRegistration: Type.Optional(Type.Any()) // Virtual relation
  },
  { $id: 'MatchResult', additionalProperties: false }
)
export type MatchResult = Static<typeof matchResultSchema>
export const matchResultValidator = getValidator(matchResultSchema, dataValidator)
export const matchResultResolver = resolve<MatchResult, HookContext<MatchResultsService>>({
  match: virtual(async (result, context) => {
    // Load the related match
    return context.app.service('matches').get(result.matchId)
  }),
  player: virtual(async (result, context) => {
    // Load the related player
    return context.app.service('players').get(result.playerId)
  }),
  matchRegistration: virtual(async (result, context) => {
    if (!result.matchRegistrationId) return undefined;
    return context.app.service('match-registrations').get(result.matchRegistrationId)
  })
})

export const matchResultExternalResolver = resolve<MatchResult, HookContext<MatchResultsService>>({})

// Schema for creating new entries
export const matchResultDataSchema = Type.Intersect(
  [
    Type.Pick(
      matchResultSchema,
      [
        'matchId',
        'playerId',
        'points',
        'maxPoints',
        'place',
        'scores',
        'styleDivision',
        'ageDivision',
        'genderDivision',
        'registrationDetails',
        'equipmentId',
        'matchRegistrationId'
      ]
    ),
    Type.Partial(
      Type.Pick(
        matchResultSchema,
        ['createdBy', 'updatedBy']
      )
    )
  ],
  {
    $id: 'MatchResultData'
  }
)
export type MatchResultData = Static<typeof matchResultDataSchema>
export const matchResultDataValidator = getValidator(matchResultDataSchema, dataValidator)
export const matchResultDataResolver = resolve<MatchResult, HookContext<MatchResultsService>>({
  scores: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  resultDate: async () => {
    return new Date().toISOString()
  },
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const matchResultPatchSchema = Type.Intersect(
  [
    Type.Partial(
      Type.Pick(matchResultSchema, [
        'points',
        'maxPoints',
        'place',
        'scores',
        'styleDivision',
        'ageDivision',
        'genderDivision',
        'registrationDetails',
        'equipmentId',
        'matchRegistrationId'
      ])
    ),
    Type.Partial(
      Type.Pick(
        matchResultSchema,
        ['updatedBy']
      )
    )
  ],
  {
    $id: 'MatchResultPatch'
  }
)
export type MatchResultPatch = Static<typeof matchResultPatchSchema>
export const matchResultPatchValidator = getValidator(matchResultPatchSchema, dataValidator)
export const matchResultPatchResolver = resolve<MatchResult, HookContext<MatchResultsService>>({
  scores: (value) => (value !== undefined && typeof value === 'object' ? JSON.stringify(value) : value),
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const matchResultQueryProperties = Type.Pick(matchResultSchema, [
  'id',
  'matchId',
  'playerId',
  'points',
  'maxPoints',
  'place',
  'resultDate'
])
export const matchResultQuerySchema = Type.Intersect(
  [
    querySyntax(matchResultQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type MatchResultQuery = Static<typeof matchResultQuerySchema>
export const matchResultQueryValidator = getValidator(matchResultQuerySchema, queryValidator)
export const matchResultQueryResolver = resolve<MatchResultQuery, HookContext<MatchResultsService>>({})
