// For more information see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import type { FederationService } from './federations.class'

// Main federation schema
export const federationSchema = Type.Object(
    {
        id: Type.Number(),
        legacyId: Type.Optional(Type.Number()),
        isActive: Type.Boolean(),
        name: Type.String(),
        fullName: Type.Optional(Type.String()),
        ageDivisions: Type.Optional(Type.Unknown()),
        styleDivisions: Type.Optional(Type.Unknown()),
        disciplines: Type.Optional(Type.Unknown()),
        createdAt: Type.Optional(Type.String()),
        updatedAt: Type.Optional(Type.String()),
        deletedAt: Type.Optional(Type.String()),
        createdBy: Type.Optional(Type.String()),
        updatedBy: Type.Optional(Type.String()),
        deletedBy: Type.Optional(Type.String())
    },
    { $id: 'Federation', additionalProperties: false }
)
export type Federation = Static<typeof federationSchema>
export const federationValidator = getValidator(federationSchema, dataValidator)
export const federationResolver = resolve<Federation, HookContext<FederationService>>({})

export const federationExternalResolver = resolve<Federation, HookContext<FederationService>>({})

// Schema for creating new federations
export const federationDataSchema = Type.Object(
    {
        legacyId: Type.Optional(Type.Number()),
        isActive: Type.Boolean(),
        name: Type.String(),
        fullName: Type.Optional(Type.String()),
        ageDivisions: Type.Optional(Type.Unknown()),
        styleDivisions: Type.Optional(Type.Unknown()),
        disciplines: Type.Optional(Type.Unknown()),
        // createdAt, updatedAt, etc. handled automatically
    },
    { $id: 'FederationData' }
)
export type FederationData = Static<typeof federationDataSchema>
export const federationDataValidator = getValidator(federationDataSchema, dataValidator)
// Add a resolver to ensure JSON fields are stringified before saving to DB
export const federationDataResolver = resolve<FederationData, HookContext<FederationService>>({
    ageDivisions: (value) => (value !== undefined ? JSON.stringify(value) : undefined),
    styleDivisions: (value) => (value !== undefined ? JSON.stringify(value) : undefined),
    disciplines: (value) => (value !== undefined ? JSON.stringify(value) : undefined)
})

// Schema for patching federations
export const federationPatchSchema = Type.Partial(federationSchema, {
    $id: 'FederationPatch'
})
export type FederationPatch = Static<typeof federationPatchSchema>
export const federationPatchValidator = getValidator(federationPatchSchema, dataValidator)
// Add a resolver to ensure JSON fields are stringified before saving to DB
export const federationPatchResolver = resolve<FederationPatch, HookContext<FederationService>>({
    ageDivisions: (value) => (value !== undefined ? JSON.stringify(value) : undefined),
    styleDivisions: (value) => (value !== undefined ? JSON.stringify(value) : undefined),
    disciplines: (value) => (value !== undefined ? JSON.stringify(value) : undefined)
})

// Allowed query properties
export const federationQueryProperties = Type.Pick(federationSchema, [
    'id',
    'legacyId',
    'isActive',
    'name',
    'fullName'
])
export const federationQuerySchema = Type.Intersect(
    [
        querySyntax(federationQueryProperties),
        Type.Object({}, { additionalProperties: false })
    ],
    { additionalProperties: false }
)

export type FederationQuery = Static<typeof federationQuerySchema>
export const federationQueryValidator = getValidator(federationQuerySchema, queryValidator)
export const federationQueryResolver = resolve<FederationQuery, HookContext<FederationService>>({})
