import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { FederationService, getOptions } from './federations.class'
import {
    federationDataResolver,
    federationDataValidator,
    federationExternalResolver,
    federationPatchResolver,
    federationPatchValidator,
    federationQueryResolver,
    federationQueryValidator,
    federationResolver
} from './federations.schema'

export const federationPath = 'federations'
export const federationMethods = ['find', 'get', 'create', 'patch', 'remove']

export * from './federations.class'
export * from './federations.schema'

export const federationHooks = {
    around: {
        all: [
            schemaHooks.resolveExternal(federationExternalResolver),
            schemaHooks.resolveResult(federationResolver)
        ]
    },
    before: {
        all: [
            schemaHooks.validateQuery(federationQueryValidator),
            schemaHooks.resolveQuery(federationQueryResolver)
        ],
        find: [],
        get: [],
        create: [
            schemaHooks.validateData(federationDataValidator),
            schemaHooks.resolveData(federationDataResolver)
        ],
        patch: [
            schemaHooks.validateData(federationPatchValidator),
            schemaHooks.resolveData(federationPatchResolver)
        ],
        remove: []
    },
    after: {
        all: []
    },
    error: {
        all: []
    }
}

export function federations(app: Application) {
    app.use(federationPath, new FederationService(getOptions(app)), {
        docs: {
            description: 'Federations service',
            definitions: {
                Federation: require('./federations.schema').federationSchema,
                FederationData: require('./federations.schema').federationDataSchema,
                FederationPatch: require('./federations.schema').federationPatchSchema
            },
            securities: ['all'],
            operations: {
                find: {
                    responses: {
                        '200': {
                            description: 'List of federations',
                            content: {
                                'application/json': {
                                    schema: {
                                        type: 'object',
                                        properties: {
                                            total: { type: 'number' },
                                            limit: { type: 'number' },
                                            skip: { type: 'number' },
                                            data: {
                                                type: 'array',
                                                items: { $ref: '#/components/schemas/Federation' }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                get: {
                    responses: {
                        '200': {
                            description: 'Get a federation',
                            content: {
                                'application/json': {
                                    schema: { $ref: '#/components/schemas/Federation' }
                                }
                            }
                        }
                    }
                },
                create: {
                    requestBody: {
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/FederationData' }
                            }
                        }
                    },
                    responses: {
                        '201': {
                            description: 'Federation created',
                            content: {
                                'application/json': {
                                    schema: { $ref: '#/components/schemas/Federation' }
                                }
                            }
                        }
                    }
                },
                patch: {
                    requestBody: {
                        content: {
                            'application/json': {
                                schema: { $ref: '#/components/schemas/FederationPatch' }
                            }
                        }
                    },
                    responses: {
                        '200': {
                            description: 'Federation patched',
                            content: {
                                'application/json': {
                                    schema: { $ref: '#/components/schemas/Federation' }
                                }
                            }
                        }
                    }
                },
                remove: {
                    responses: {
                        '200': {
                            description: 'Federation removed',
                            content: {
                                'application/json': {
                                    schema: { $ref: '#/components/schemas/Federation' }
                                }
                            }
                        }
                    }
                }
            }
        }
    })
    // Register hooks
    app.service(federationPath).hooks(federationHooks)
}
