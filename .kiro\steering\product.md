# Product Overview

**Archery Points API** - A comprehensive REST API and real-time application for managing archery competitions and tournaments.

## Core Features
- **Tournament Management**: Create and manage archery tournaments with various formats
- **Match System**: Handle match creation, registration, and results tracking
- **Player Management**: Manage player profiles, equipment, and performance data
- **Real-time Communication**: WebSocket support for live updates and messaging
- **Authentication**: Multi-provider OAuth (GitHub, Google, Facebook) and local auth
- **Federation Support**: Multi-federation tournament organization

## Key Entities
- Users, Players, Organizers
- Tournaments, Matches, Match Registrations
- Equipment, Federations, Clubs
- Messages, Match Results

## Target Users
- Tournament organizers
- Archery clubs and federations
- Individual archers and players
- Competition management systems