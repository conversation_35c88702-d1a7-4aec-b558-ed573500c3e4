import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populateCount } from '../../hooks/populate'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { validateOrganizer } from './hooks/validate-organizer'
import { validateTournament } from './hooks/validate-tournament'
import { MatchesService, getOptions } from './matches.class'
import {
  matchDataResolver,
  matchDataSchema,
  matchDataValidator,
  matchExternalResolver,
  matchPatchResolver,
  matchPatchSchema,
  matchPatchValidator,
  matchQueryResolver,
  matchQuerySchema,
  matchQueryValidator,
  matchResolver,
  matchSchema
} from './matches.schema'
import { matchesMethods, matchesPath } from './matches.shared'

export * from './matches.class'
export * from './matches.schema'

// A configure function that registers the service and its hooks via `app.configure`
export const matches = (app: Application) => {
  // Register our service on the Feathers application
  app.use(matchesPath, new MatchesService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: matchesMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { matchDataSchema, matchQuerySchema, matchSchema, matchPatchSchema },
      docs: {
        description: 'Matches service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(matchesPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(matchExternalResolver),
        schemaHooks.resolveResult(matchResolver)
      ]
    },
    before: {
      all: [skipIfDeletedByHook(), schemaHooks.validateQuery(matchQueryValidator), schemaHooks.resolveQuery(matchQueryResolver)],
      find: [populateCount({
        'match-registrations': {
          service: 'match-registrations',
          foreignKey: 'matchId'
        }
      })],
      get: [populateCount({
        'match-registrations': {
          service: 'match-registrations',
          foreignKey: 'matchId'
        }
      })],
      create: [
        schemaHooks.validateData(matchDataValidator),
        validateOrganizer(),
        validateTournament(),
        schemaHooks.resolveData(matchDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(matchPatchValidator),
        validateOrganizer(),
        validateTournament(),
        schemaHooks.resolveData(matchPatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      find: [populateCount({
        'match-registrations': {
          service: 'match-registrations',

          foreignKey: 'matchId'
        }
      })],
      get: [populateCount({
        'match-registrations': {
          service: 'match-registrations',
          foreignKey: 'matchId'
        }
      })]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [matchesPath]: MatchesService
  }
}
