// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { federationSchema } from '../federations/federations.schema'
import { organizerSchema } from '../organizers/organizers.schema'
import type { TournamentsService } from './tournaments.class'

// Main data model schema
export const tournamentSchema = Type.Object(
  {
    id: Type.Number(),
    organizerId: Type.Number(),
    name: Type.String(),
    description: Type.Optional(Type.String()),
    coverImageUrl: Type.Optional(Type.String()),
    icon: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    legacyId: Type.Optional(Type.Number()),
    styleDivisions: Type.Optional(Type.Unknown()),
    ageDivisions: Type.Optional(Type.Unknown()),
    forWomen: Type.Optional(Type.Boolean()),
    forMen: Type.Optional(Type.Boolean()),
    isOpen: Type.Optional(Type.Boolean()),
    federationId: Type.Optional(Type.Number()),
    licenseRequired: Type.Optional(Type.Boolean()),
    international: Type.Optional(Type.Boolean()),
    completedAt: Type.Optional(Type.Union([
      Type.String({ format: 'date-time' }),
      Type.Null()
    ])),
    rulesSettings: Type.Optional(Type.String()),
    totalRounds: Type.Optional(Type.Number()),
    minRounds: Type.Optional(Type.Number()),
    agenda: Type.Optional(Type.Unknown()),
    generalScore: Type.Optional(Type.Unknown()),
    competitionLevel: Type.Optional(Type.String()),
    yearly: Type.Optional(Type.Boolean()),
    createdAt: Type.Optional(Type.String({ format: 'date-time' })),
    updatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    // Virtual fields for related entities
    organizer: Type.Optional(Type.Ref(organizerSchema)),
    federation: Type.Optional(Type.Ref(federationSchema)),
    matches: Type.Optional(Type.Array(Type.Unknown()))
  },
  { $id: 'Tournament', additionalProperties: false }
)
export type Tournament = Static<typeof tournamentSchema>
export const tournamentValidator = getValidator(tournamentSchema, dataValidator)
export const tournamentResolver = resolve<Tournament, HookContext<TournamentsService>>({

})

export const tournamentExternalResolver = resolve<Tournament, HookContext<TournamentsService>>({})

// Schema for creating new entries
export const tournamentDataSchema = Type.Pick(
  tournamentSchema,
  ['organizerId', 'name', 'description', 'coverImageUrl', 'icon', 'isActive', 'legacyId'],
  {
    $id: 'TournamentData'
  }
)
export type TournamentData = Static<typeof tournamentDataSchema>
export const tournamentDataValidator = getValidator(tournamentDataSchema, dataValidator)
export const tournamentDataResolver = resolve<Tournament, HookContext<TournamentsService>>({
  createdAt: async () => {
    return new Date().toISOString()
  },
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for updating existing entries
export const tournamentPatchSchema = Type.Partial(tournamentSchema, {
  $id: 'TournamentPatch'
})
export type TournamentPatch = Static<typeof tournamentPatchSchema>
export const tournamentPatchValidator = getValidator(tournamentPatchSchema, dataValidator)
export const tournamentPatchResolver = resolve<Tournament, HookContext<TournamentsService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const tournamentQueryProperties = Type.Pick(tournamentSchema, [
  'id',
  'organizerId',
  'name',
  'isActive',
  'legacyId',
  'completedAt'
])
export const tournamentQuerySchema = Type.Intersect(
  [
    querySyntax(tournamentQueryProperties),
    // Add additional query properties here
    Type.Object({
      $populate: Type.Optional(Type.Union([
        Type.String(),
        Type.Array(Type.String())
      ]))
    }, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type TournamentQuery = Static<typeof tournamentQuerySchema>
export const tournamentQueryValidator = getValidator(tournamentQuerySchema, queryValidator)
export const tournamentQueryResolver = resolve<TournamentQuery, HookContext<TournamentsService>>({})
