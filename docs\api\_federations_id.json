{"endpoint": "/federations/{id}", "methods": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of federations to return", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "Get a federation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Federation"}}}}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["federations"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of federations to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/federations"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["federations"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/federations"}}}}}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of federations to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "Federation patched", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Federation"}}}}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["federations"], "security": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FederationPatch"}}}}}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of federations to remove", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "Federation removed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Federation"}}}}}, "description": "Removes the resource with id.", "summary": "", "tags": ["federations"], "security": []}}}