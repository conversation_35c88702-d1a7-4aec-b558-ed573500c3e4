{"name": "MatchResult", "schema": {"required": ["id", "matchId", "playerId", "points", "maxPoints", "resultDate", "createdAt", "updatedAt"], "type": "object", "properties": {"id": {"type": "number"}, "matchId": {"type": "number"}, "playerId": {"type": "number"}, "matchRegistrationId": {"type": "number"}, "points": {"type": "number"}, "maxPoints": {"type": "number"}, "place": {"type": "number"}, "scores": {}, "styleDivision": {"type": "string"}, "ageDivision": {"type": "string"}, "genderDivision": {"type": "string"}, "registrationDetails": {}, "equipmentId": {"type": "number"}, "resultDate": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "match": {"$ref": "#/components/schemas/Match"}, "player": {"$ref": "#/components/schemas/Player"}, "matchRegistration": {}}, "additionalProperties": false}}