// Country code to country name mapping
export const COUNTRY_CODES: Record<string, string> = {
  'pl': 'Polska',
  'cz': 'Czechia',
  'sk': 'Slovakia',
  'hu': 'Hungary',
  'de': 'Germany',
  'at': 'Austria',
  'fr': 'France',
  'gb': 'United Kingdom',
  'us': 'United States',
  'ca': 'Canada',
  'au': 'Australia',
  'it': 'Italy',
  'es': 'Spain',
  'pt': 'Portugal',
  'nl': 'Netherlands',
  'be': 'Belgium',
  'ch': 'Switzerland',
  'se': 'Sweden',
  'no': 'Norway',
  'dk': 'Denmark',
  'fi': 'Finland',
  'ie': 'Ireland',
  'lu': 'Luxembourg',
  'si': 'Slovenia',
  'hr': 'Croatia',
  'rs': 'Serbia',
  'bg': 'Bulgaria',
  'ro': 'Romania',
  'gr': 'Greece',
  'cy': 'Cyprus',
  'mt': 'Malta',
  'ee': 'Estonia',
  'lv': 'Latvia',
  'lt': 'Lithuania'
}

/**
 * Convert country code to country name
 * @param countryCode - Two-letter country code (e.g., 'pl', 'cz')
 * @returns Country name or the original code if not found
 */
export function getCountryName(countryCode: string | undefined): string {
  if (!countryCode) return 'Polska' // Default to Poland
  
  const code = countryCode.toLowerCase()
  return COUNTRY_CODES[code] || countryCode
}

/**
 * Get country code from country name
 * @param countryName - Country name
 * @returns Country code or undefined if not found
 */
export function getCountryCode(countryName: string): string | undefined {
  const name = countryName.toLowerCase()
  for (const [code, country] of Object.entries(COUNTRY_CODES)) {
    if (country.toLowerCase() === name) {
      return code
    }
  }
  return undefined
}

/**
 * Get all available countries as options for select components
 */
export function getCountryOptions(): Array<{ value: string; label: string }> {
  return Object.entries(COUNTRY_CODES).map(([code, name]) => ({
    value: name,
    label: name
  }))
}
